import { createClient } from "@/lib/server";
import { openai } from "@ai-sdk/openai";
import { streamText } from "ai";
import { cookies } from "next/headers";

export const maxDuration = 30;

export async function POST(req: Request) {
	try {
		// Verify user authentication
		const cookieStore = await cookies();
		const supabase = await createClient();

		const {
			data: { session },
		} = await supabase.auth.getSession();

		if (!session) {
			return new Response("Unauthorized", { status: 401 });
		}

		const { messages, conversationId } = await req.json();

		// Get system prompt from conversation if conversationId is provided
		let systemPrompt = `You are <PERSON><PERSON><PERSON><PERSON>, a helpful and intelligent AI assistant. You provide thoughtful, accurate, and engaging responses while maintaining a friendly and professional tone. Always aim to be helpful while being concise and clear in your explanations.`;

		if (conversationId) {
			const { data: conversation } = await supabase
				.from("conversations")
				.select("system_prompt")
				.eq("id", conversationId)
				.eq("user_id", session.user.id)
				.single();

			if (conversation?.system_prompt) {
				systemPrompt = conversation.system_prompt;
			}
		}

		// Stream the AI response
		const result = streamText({
			model: openai("gpt-4o"),
			messages,
			system: systemPrompt,
		});

		return result.toDataStreamResponse();
	} catch (error) {
		console.error("Chat API error:", error);
		return new Response("Internal Server Error", { status: 500 });
	}
}
