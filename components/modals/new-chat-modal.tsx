"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { createClient } from "@/lib/client";
import { useUser } from "@/contexts/user-context";
import { useToast } from "@/components/ui/use-toast";
import { useActiveConversationId } from "@/hooks/atom/conversation-id-atom";

interface NewChatModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSuccess?: () => void;
}

export function NewChatModal({ open, onOpenChange, onSuccess }: NewChatModalProps) {
	const [title, setTitle] = useState("");
	const [systemPrompt, setSystemPrompt] = useState(
		"You are <PERSON><PERSON><PERSON><PERSON>, a helpful and intelligent AI assistant. You provide thoughtful, accurate, and engaging responses while maintaining a friendly and professional tone."
	);
	const [isLoading, setIsLoading] = useState(false);
	
	const router = useRouter();
	const { user } = useUser();
	const { toast } = useToast();
	const { setActiveConversationId } = useActiveConversationId();
	const supabase = createClient();

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		
		if (!user || !title.trim()) {
			toast({
				title: "Error",
				description: "Please enter a title for your conversation.",
				variant: "destructive",
			});
			return;
		}

		setIsLoading(true);

		try {
			// Create new conversation with title and system prompt
			const { data: conversation, error } = await supabase
				.from("conversations")
				.insert({
					user_id: user.id,
					title: title.trim(),
					system_prompt: systemPrompt.trim() || null,
				})
				.select()
				.single();

			if (error) throw error;

			// Set the active conversation ID
			setActiveConversationId(conversation.id);

			// Call onSuccess callback to refetch conversations
			onSuccess?.();

			// Close modal and reset form
			onOpenChange(false);
			setTitle("");
			setSystemPrompt(
				"You are ProjectPersona, a helpful and intelligent AI assistant. You provide thoughtful, accurate, and engaging responses while maintaining a friendly and professional tone."
			);

			// Redirect to the new conversation
			router.push(`/chat/${conversation.id}`);

			toast({
				title: "Success",
				description: "New conversation created successfully!",
			});
		} catch (error) {
			console.error("Error creating conversation:", error);
			toast({
				title: "Error",
				description: "Failed to create new conversation. Please try again.",
				variant: "destructive",
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleCancel = () => {
		onOpenChange(false);
		setTitle("");
		setSystemPrompt(
			"You are ProjectPersona, a helpful and intelligent AI assistant. You provide thoughtful, accurate, and engaging responses while maintaining a friendly and professional tone."
		);
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle>Create New Chat</DialogTitle>
					<DialogDescription>
						Set up a new conversation with a custom title and system prompt.
					</DialogDescription>
				</DialogHeader>
				
				<form onSubmit={handleSubmit} className="space-y-4">
					<div className="space-y-2">
						<Label htmlFor="title">Title</Label>
						<Input
							id="title"
							placeholder="Enter conversation title..."
							value={title}
							onChange={(e) => setTitle(e.target.value)}
							disabled={isLoading}
							required
						/>
					</div>
					
					<div className="space-y-2">
						<Label htmlFor="systemPrompt">System Prompt</Label>
						<Textarea
							id="systemPrompt"
							placeholder="Enter system prompt for the AI assistant..."
							value={systemPrompt}
							onChange={(e) => setSystemPrompt(e.target.value)}
							disabled={isLoading}
							rows={4}
							className="resize-none"
						/>
						<p className="text-xs text-muted-foreground">
							This defines how the AI assistant should behave in this conversation.
						</p>
					</div>
					
					<DialogFooter>
						<Button
							type="button"
							variant="outline"
							onClick={handleCancel}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button type="submit" disabled={isLoading || !title.trim()}>
							{isLoading ? "Creating..." : "Create Chat"}
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
